from __future__ import annotations
import asyncio
import json
import sys

from typing import Optional, List, Any, Dict, Iterable
from contextlib import AsyncExitStack
from urllib.parse import urlparse

from mcp import ClientSession
from mcp.client.streamable_http import streamablehttp_client
from mcp.types import TextContent

from openai import AsyncOpenAI
from openai.types.responses import Response

from dotenv import load_dotenv
from openai.types.responses.tool_param import ToolParam

load_dotenv()  # Load environment variables from .env file

MODEL = "gpt-4o-mini"


class GPTClient:

    def __init__(self):
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.openai = AsyncOpenAI()
        self.conversation_history = []
        self.server_url = ''

    async def connect(self, server_url: str):
        """Connect to MCP server over HTTP transport
        
        Args:
					server_url: HTTP URL of the MCP server (e.g., 'http://localhost:8000/mcp')
        """
        try:
            parsed = urlparse(server_url)
            if not parsed.scheme or not parsed.netloc:
                raise ValueError(f"Invalid server URL: {server_url}")
            if parsed.scheme not in ("http", "https"):
                raise ValueError("URL scheme must be http or https")
            
            self.server_url = server_url
        except Exception as e:
            raise ValueError(f"Invalid server URL '{server_url}': {e}")
        
        try:
            print("Establishing HTTP transport connection...")
            http_transport = await asyncio.wait_for(
                self.exit_stack.enter_async_context(streamablehttp_client(server_url)),
                timeout=30.0
            )
            self.read_stream, self.write_stream, self.session_id = http_transport

            print("Creating MCP session...")
            self.session = await self.exit_stack.enter_async_context(ClientSession(self.read_stream, self.write_stream))

            print("Initializing session...")
            await asyncio.wait_for(self.session.initialize(), timeout=10.0)  # Wait for initialization to complete within 10 seconds

            print("Discovering available tools...")
            toolsData = await self.session.list_tools()
            tools = toolsData.tools
            print(f"\n✅ Connected to MCP server at {server_url}")
            print(f"📋 Available tools: {json.dumps([tool.name for tool in tools], indent=2)}")

            if self.session_id:
                print(f"🔗 Session ID: {self.session_id}")

        except Exception as e:
            raise RuntimeError(f"Failed to connect to MCP server at {server_url}: {e}")

    async def disconnect(self):
        try:
            if self.exit_stack:
                print("Disconnecting from MCP server...")
                await self.exit_stack.aclose()
                print("✅ Disconnected successfully")
        except Exception as e:
            print(f"⚠️  Warning during disconnect: {e}")

    async def query(self, query: str) -> str:
        """Process query using openai and available tools"""
        self.conversation_history.append({
            "role": "user",
            "content": query,
        })
        messages = self.conversation_history.copy()

        if not self.session:
            raise RuntimeError("Not connected to MCP server. Call connect() first.")
        
        toolData = await self.session.list_tools()
        available_tools: List[Dict[str, Any]] = []
        tools: Iterable[ToolParam] = []
        for tool in toolData.tools:
            tool_param: Dict[str, Any] = {
                "type": "mcp",
                "server_label": tool.name,
                "server_url": self.server_url,
                "require_approval": "never",
                
                # "name": tool.name,
                # "description": tool.description or "",
                # "input_schema": tool.inputSchema,
            }
            available_tools.append(tool_param)

        print(f"Available tools: {json.dumps(available_tools, indent=2)}")
        response = self.openai.responses.create(
            model=MODEL,
            previous_response_id=None,
            tools=available_tools
            # tools=[{
            #     "type": "mcp",
            #     "server_label": "deepwiki",
            #     "server_url": self.server_url,
            #     "require_approval": "never",
            #     # "allowed_tools": ["ask_question"],
            # }]
        )

    async def chat(self):
        while True:
            try:
                query = input("> ").strip()
                if query.lower() in ("quit", "exit", "q"):
                    print("Alright then, Goodbye!")
                    break

                if query.lower() == "clear":
                    self.conversation_history.clear()
                    continue

                if not query:
                    continue

                print("🤔 GPT is thinking...")
                response = await self.query(query)
                print("\n" + "=" * 50)
                print("🤖 GPT's response:")
                print("=" * 50)
                print(response)
                print("=" * 50 + "\n")

            except KeyboardInterrupt:
                print("\n\nChat interrupted. Type 'quit' to exit gracefully.")
                break
            except ConnectionError as e:
                print(f"\n❌ Connection error: {e}")
                print("The MCP server may have disconnected. Please restart the client.")
                break
            except Exception as e:
                print(f"\n❌ Error: {str(e)}")


async def main():
    if len(sys.argv) == 1:
        server_url = "http://localhost:8000/mcp"
        print(f"No server URL provided, using default: {server_url}")
    elif len(sys.argv) == 2:
        server_url = sys.argv[1]
    else:
        print("Usage: python gptclient.py [server_url]")
        print("Examples:")
        print("  python gptclient.py")
        print("  python gptclient.py http://localhost:8000/mcp")
        print("  python gptclient.py https://your-server.com/mcp")
        sys.exit(1)

    client = GPTClient()
    try:
        print(f"Connecting to MCP server at {server_url}...")
        await client.connect(server_url)
        await client.chat()
    except KeyboardInterrupt:
        print("\n\nInterrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure the MCP server is running")
        print("2. Verify the server URL is correct")
        print("3. Check that the server supports HTTP transport")
        print("4. Ensure your ANTHROPIC_API_KEY environment variable is set")
    finally:
        await client.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
